'use client';

import React, { useState, useRef, useCallback } from 'react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Plus,
    CloudUpload,
    FileText,
    GitBranch,
    FolderPlus,
    X,
    CheckCircle,
    AlertCircle,
    Loader2,
    Upload,
    Check,
    FileIcon,
} from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '@/lib/supabase/client';
import { FileNameValidator, useNameValidation } from '@/lib/validation';
import { cn } from '@/lib/utils';
import { type Folder } from '@/hooks/react-query/knowledge-base/use-folders';

interface FileUploadStatus {
    file: File;
    status: 'queued' | 'uploading' | 'success' | 'error';
    progress: number;
    error?: string;
}

interface UnifiedKbEntryModalProps {
    folders: Folder[];
    onUploadComplete: () => void;
    trigger?: React.ReactNode;
    defaultTab?: 'upload' | 'text' | 'git';
}

const API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';

export function UnifiedKbEntryModal({
    folders,
    onUploadComplete,
    trigger,
    defaultTab = 'upload'
}: UnifiedKbEntryModalProps) {
    const [isOpen, setIsOpen] = useState(false);
    const [activeTab, setActiveTab] = useState(defaultTab);
    const [selectedFolder, setSelectedFolder] = useState<string>('');
    const [isCreatingFolder, setIsCreatingFolder] = useState(false);
    const [newFolderName, setNewFolderName] = useState('');
    const [isEditingNewFolder, setIsEditingNewFolder] = useState(false);
    
    // File upload state
    const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
    const [uploadStatuses, setUploadStatuses] = useState<FileUploadStatus[]>([]);
    const [isDragOver, setIsDragOver] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    
    // Text entry state
    const [filename, setFilename] = useState('');
    const [content, setContent] = useState('');
    const [isCreatingText, setIsCreatingText] = useState(false);
    
    // Git clone state
    const [gitUrl, setGitUrl] = useState('');
    const [gitBranch, setGitBranch] = useState('main');
    const [isCloning, setIsCloning] = useState(false);
    
    const fileInputRef = useRef<HTMLInputElement>(null);
    const newFolderInputRef = useRef<HTMLInputElement>(null);
    
    // Validation for new folder name
    const existingFolderNames = folders.map(f => f.name);
    const folderValidation = useNameValidation(newFolderName, 'folder', existingFolderNames);
    
    // Validation for filename
    const filenameValidation = useNameValidation(filename, 'file');

    const handleFolderCreation = async () => {
        if (!folderValidation.isValid) {
            toast.error(folderValidation.friendlyError || 'Invalid folder name');
            return;
        }

        setIsCreatingFolder(true);
        try {
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.access_token) {
                throw new Error('No session found');
            }

            const response = await fetch(`${API_URL}/knowledge-base/folders`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: newFolderName.trim()
                })
            });

            if (response.ok) {
                const newFolder = await response.json();
                toast.success('Folder created successfully');
                
                // Refresh folders list
                onUploadComplete();
                
                // Select the new folder
                setSelectedFolder(newFolder.folder_id);
                
                // Reset state
                setNewFolderName('');
                setIsEditingNewFolder(false);
            } else {
                const errorData = await response.json().catch(() => null);
                toast.error(errorData?.detail || 'Failed to create folder');
            }
        } catch (error) {
            console.error('Error creating folder:', error);
            toast.error('Failed to create folder');
        } finally {
            setIsCreatingFolder(false);
        }
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files) {
            const fileArray = Array.from(files);
            addFiles(fileArray);
        }
    };

    const addFiles = (newFiles: File[]) => {
        setSelectedFiles(prev => [...prev, ...newFiles]);
        setUploadStatuses(prev => [
            ...prev,
            ...newFiles.map(file => ({
                file,
                status: 'queued' as const,
                progress: 0
            }))
        ]);
    };

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
        
        const files = Array.from(e.dataTransfer.files);
        if (files.length > 0) {
            addFiles(files);
        }
    }, []);

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    }, []);

    const removeFile = (index: number) => {
        setSelectedFiles(prev => prev.filter((_, i) => i !== index));
        setUploadStatuses(prev => prev.filter((_, i) => i !== index));
    };

    const handleFileUpload = async () => {
        if (!selectedFolder) {
            toast.error('Please select a folder');
            return;
        }

        if (selectedFiles.length === 0) {
            toast.error('Please select files to upload');
            return;
        }

        setIsUploading(true);

        try {
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.access_token) {
                throw new Error('No session found');
            }

            let completedFiles = 0;

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];

                setUploadStatuses(prev => prev.map((status, index) =>
                    index === i ? { ...status, status: 'uploading', progress: 0 } : status
                ));

                try {
                    const formData = new FormData();
                    formData.append('file', file);

                    const response = await fetch(`${API_URL}/knowledge-base/folders/${selectedFolder}/upload`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${session.access_token}`,
                        },
                        body: formData
                    });

                    if (response.ok) {
                        setUploadStatuses(prev => prev.map((status, index) =>
                            index === i ? { ...status, status: 'success', progress: 100 } : status
                        ));
                        completedFiles++;
                    } else {
                        let errorMessage = `Upload failed: ${response.status}`;
                        if (response.status === 413) {
                            try {
                                const errorData = await response.json();
                                errorMessage = errorData.detail || 'Knowledge base limit (50MB) exceeded';
                            } catch {
                                errorMessage = 'Knowledge base limit (50MB) exceeded';
                            }
                        }

                        setUploadStatuses(prev => prev.map((status, index) =>
                            index === i ? {
                                ...status,
                                status: 'error',
                                progress: 0,
                                error: errorMessage
                            } : status
                        ));
                    }
                } catch (fileError) {
                    setUploadStatuses(prev => prev.map((status, index) =>
                        index === i ? {
                            ...status,
                            status: 'error',
                            progress: 0,
                            error: `Upload failed: ${fileError}`
                        } : status
                    ));
                }
            }

            if (completedFiles === selectedFiles.length) {
                toast.success(`Successfully uploaded ${completedFiles} file(s)`);
                resetAndClose();
            } else if (completedFiles > 0) {
                toast.success(`Uploaded ${completedFiles} of ${selectedFiles.length} files`);
            } else {
                toast.error('Failed to upload files');
            }

            onUploadComplete();

        } catch (error) {
            console.error('Error uploading files:', error);
            toast.error('Failed to upload files');
        } finally {
            setIsUploading(false);
        }
    };

    const handleTextCreate = async () => {
        if (!selectedFolder) {
            toast.error('Please select a folder');
            return;
        }

        if (!filenameValidation.isValid) {
            toast.error(filenameValidation.friendlyError || 'Invalid filename');
            return;
        }

        if (!content.trim()) {
            toast.error('Please enter some content');
            return;
        }

        setIsCreatingText(true);

        try {
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.access_token) {
                throw new Error('No session found');
            }

            const finalFilename = filename.includes('.') ? filename.trim() : `${filename.trim()}.txt`;
            const textBlob = new Blob([content], { type: 'text/plain' });
            const file = new File([textBlob], finalFilename, { type: 'text/plain' });

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch(`${API_URL}/knowledge-base/folders/${selectedFolder}/upload`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                },
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                toast.success('Text entry created successfully');

                if (result.filename_changed) {
                    toast.info(`File was renamed to "${result.final_filename}" to avoid conflicts`);
                }

                onUploadComplete();
                resetAndClose();
            } else {
                const errorData = await response.json().catch(() => null);
                toast.error(errorData?.detail || 'Failed to create text entry');
            }
        } catch (error) {
            console.error('Error creating text entry:', error);
            toast.error('Failed to create text entry');
        } finally {
            setIsCreatingText(false);
        }
    };

    const handleGitClone = async () => {
        if (!selectedFolder) {
            toast.error('Please select a folder');
            return;
        }

        if (!gitUrl.trim()) {
            toast.error('Please enter a Git repository URL');
            return;
        }

        setIsCloning(true);

        try {
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();

            if (!session?.access_token) {
                throw new Error('No session found');
            }

            const response = await fetch(`${API_URL}/knowledge-base/folders/${selectedFolder}/clone-git-repo`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${session.access_token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    git_url: gitUrl.trim(),
                    branch: gitBranch.trim() || 'main'
                })
            });

            if (response.ok) {
                toast.success('Repository cloning started. Processing in background.');
                onUploadComplete();
                resetAndClose();
            } else {
                const errorData = await response.json().catch(() => null);
                toast.error(errorData?.detail || 'Failed to clone repository');
            }
        } catch (error) {
            console.error('Error cloning repository:', error);
            toast.error('Failed to clone repository');
        } finally {
            setIsCloning(false);
        }
    };

    const resetAndClose = () => {
        setTimeout(() => {
            setSelectedFiles([]);
            setUploadStatuses([]);
            setFilename('');
            setContent('');
            setGitUrl('');
            setGitBranch('main');
            setSelectedFolder('');
            setNewFolderName('');
            setIsEditingNewFolder(false);
            setIsOpen(false);
        }, 1500);
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    const selectedFolderInfo = folders.find(f => f.folder_id === selectedFolder);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                {trigger || (
                    <Button className="gap-2">
                        <Plus className="h-4 w-4" />
                        Add Knowledge
                    </Button>
                )}
            </DialogTrigger>
            <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                <DialogHeader className="flex-shrink-0">
                    <DialogTitle className="text-xl font-semibold">Add to Knowledge Base</DialogTitle>
                    <p className="text-sm text-muted-foreground">
                        Upload files, create text entries, or clone repositories
                    </p>
                </DialogHeader>

                <div className="flex-1 overflow-y-auto">
                    <div className="space-y-6 p-1">
                        {/* Folder Selection */}
                        <div className="space-y-3">
                            <Label className="text-sm font-medium">
                                Destination Folder
                            </Label>
                                    
                                    {isEditingNewFolder ? (
                                        <div className="flex items-center gap-2">
                                            <Input
                                                ref={newFolderInputRef}
                                                placeholder="Enter folder name..."
                                                value={newFolderName}
                                                onChange={(e) => setNewFolderName(e.target.value)}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter' && folderValidation.isValid) {
                                                        handleFolderCreation();
                                                    } else if (e.key === 'Escape') {
                                                        setIsEditingNewFolder(false);
                                                        setNewFolderName('');
                                                    }
                                                }}
                                                className={cn(
                                                    "flex-1",
                                                    !folderValidation.isValid && newFolderName && "border-red-500"
                                                )}
                                                disabled={isCreatingFolder}
                                            />
                                            <Button
                                                size="sm"
                                                onClick={handleFolderCreation}
                                                disabled={!folderValidation.isValid || isCreatingFolder}
                                                className="gap-1"
                                            >
                                                {isCreatingFolder ? (
                                                    <Loader2 className="h-3 w-3 animate-spin" />
                                                ) : (
                                                    <Check className="h-3 w-3" />
                                                )}
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="ghost"
                                                onClick={() => {
                                                    setIsEditingNewFolder(false);
                                                    setNewFolderName('');
                                                }}
                                                disabled={isCreatingFolder}
                                            >
                                                <X className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="flex gap-2">
                                            <select 
                                                value={selectedFolder}
                                                onChange={(e) => setSelectedFolder(e.target.value)}
                                                className="flex-1 h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
                                                disabled={folders.length === 0}
                                            >
                                                <option value="">
                                                    {folders.length === 0 ? 'No folders available' : 'Choose a folder...'}
                                                </option>
                                                {folders.map((folder) => (
                                                    <option key={folder.folder_id} value={folder.folder_id}>
                                                        {folder.name} ({folder.entry_count} files)
                                                    </option>
                                                ))}
                                            </select>
                                            <Button
                                                type="button"
                                                variant="outline"
                                                className="h-10"
                                                onClick={() => {
                                                    setIsEditingNewFolder(true);
                                                    setTimeout(() => {
                                                        newFolderInputRef.current?.focus();
                                                    }, 100);
                                                }}
                                            >
                                                <FolderPlus className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    )}
                                    
                            {!folderValidation.isValid && newFolderName && (
                                <p className="text-sm text-red-600">{folderValidation.friendlyError}</p>
                            )}
                        </div>

                        {/* Content Creation Tabs */}
                        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as typeof activeTab)}>
                            <TabsList className="grid w-full grid-cols-3">
                                <TabsTrigger value="upload" className="gap-2">
                                    <CloudUpload className="h-4 w-4" />
                                    Upload Files
                                </TabsTrigger>
                                <TabsTrigger value="text" className="gap-2">
                                    <FileText className="h-4 w-4" />
                                    Text Entry
                                </TabsTrigger>
                                <TabsTrigger value="git" className="gap-2" disabled>
                                    <GitBranch className="h-4 w-4" />
                                    Git Clone
                                    <span className="text-xs text-muted-foreground ml-1">(Coming Soon)</span>
                                </TabsTrigger>
                            </TabsList>

                            <TabsContent value="upload" className="space-y-4 mt-6">
                                <div className="space-y-4">
                                    {/* File Drop Zone */}
                                    <div
                                        className={cn(
                                            "relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200",
                                            isDragOver 
                                                ? "border-foreground bg-muted/50" 
                                                : "border-border hover:border-muted-foreground hover:bg-muted/30"
                                        )}
                                        onDragOver={handleDragOver}
                                        onDragLeave={handleDragLeave}
                                        onDrop={handleDrop}
                                    >
                                        <input
                                            ref={fileInputRef}
                                            type="file"
                                            multiple
                                            onChange={handleFileSelect}
                                            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                            disabled={isUploading}
                                        />
                                        <div className="flex flex-col items-center gap-4">
                                            <CloudUpload className="h-8 w-8 text-muted-foreground" />
                                            <div>
                                                <p className="font-medium">
                                                    {isDragOver ? 'Drop files here' : 'Drag & drop files here'}
                                                </p>
                                                <p className="text-sm text-muted-foreground mt-1">
                                                    or <button 
                                                        type="button"
                                                        className="underline font-medium"
                                                        onClick={() => fileInputRef.current?.click()}
                                                    >
                                                        browse files
                                                    </button> to upload
                                                </p>
                                            </div>
                                            <p className="text-xs text-muted-foreground">
                                                Supports PDF, DOC, TXT, MD, CSV, and more • Max 50MB total
                                            </p>
                                        </div>
                                    </div>

                                    {/* Selected Files */}
                                    {selectedFiles.length > 0 && (
                                        <div className="space-y-3">
                                            <Label className="text-sm font-medium">
                                                Selected Files ({selectedFiles.length})
                                            </Label>
                                            <div className="space-y-2 max-h-48 overflow-y-auto border rounded-lg p-3">
                                                {uploadStatuses.map((status, index) => (
                                                    <div key={index} className="flex items-center gap-3 p-2 rounded border">
                                                        <FileIcon className="h-4 w-4 text-muted-foreground" />
                                                        <div className="flex-1 min-w-0">
                                                            <div className="flex items-center justify-between">
                                                                <p className="text-sm font-medium truncate">{status.file.name}</p>
                                                                <div className="flex items-center gap-2 ml-2">
                                                                    {status.status === 'uploading' && (
                                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                                    )}
                                                                    {status.status === 'success' && (
                                                                        <CheckCircle className="h-4 w-4" />
                                                                    )}
                                                                    {status.status === 'error' && (
                                                                        <AlertCircle className="h-4 w-4" />
                                                                    )}
                                                                    {status.status === 'queued' && !isUploading && (
                                                                        <Button
                                                                            variant="ghost"
                                                                            size="sm"
                                                                            onClick={() => removeFile(index)}
                                                                            className="h-6 w-6 p-0"
                                                                        >
                                                                            <X className="h-3 w-3" />
                                                                        </Button>
                                                                    )}
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center justify-between mt-1">
                                                                <span className="text-xs text-muted-foreground">
                                                                    {formatFileSize(status.file.size)}
                                                                </span>
                                                                <span className="text-xs">
                                                                    {status.status === 'success' ? 'Uploaded' :
                                                                     status.status === 'error' ? 'Failed' :
                                                                     status.status === 'uploading' ? 'Uploading...' :
                                                                     'Ready'}
                                                                </span>
                                                            </div>
                                                            {status.status === 'error' && status.error && (
                                                                <p className="text-xs text-red-600 mt-1">{status.error}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </TabsContent>

                            <TabsContent value="text" className="space-y-4 mt-6">
                                <div className="space-y-4">

                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="filename" className="text-sm font-medium">
                                                Filename
                                            </Label>
                                            <Input
                                                id="filename"
                                                placeholder="e.g., notes.txt or documentation.md"
                                                value={filename}
                                                onChange={(e) => setFilename(e.target.value)}
                                                className={cn(
                                                    !filenameValidation.isValid && filename && "border-red-500"
                                                )}
                                            />
                                            {!filenameValidation.isValid && filename && (
                                                <p className="text-sm text-red-600">{filenameValidation.friendlyError}</p>
                                            )}
                                            <p className="text-xs text-muted-foreground">
                                                Will default to .txt extension if none specified
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <Label className="text-sm font-medium">
                                                Preview
                                            </Label>
                                            <div className="h-10 px-3 py-2 bg-muted border rounded-md flex items-center">
                                                <FileText className="h-4 w-4 text-muted-foreground mr-2" />
                                                <span className="text-sm text-muted-foreground">
                                                    {filename ? (filename.includes('.') ? filename : `${filename}.txt`) : 'filename.txt'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="content" className="text-sm font-medium">
                                            Content
                                        </Label>
                                        <Textarea
                                            id="content"
                                            placeholder="Enter your text content here..."
                                            value={content}
                                            onChange={(e) => setContent(e.target.value)}
                                            rows={16}
                                            className="resize-none min-h-[200px] max-h-[200px]"
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            This content will be saved as a searchable text file in your knowledge base
                                        </p>
                                    </div>
                                </div>
                            </TabsContent>

                            <TabsContent value="git" className="space-y-4 mt-6">
                                <div className="space-y-4">

                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <Label htmlFor="git-url" className="text-sm font-medium">
                                                Repository URL
                                            </Label>
                                            <Input
                                                id="git-url"
                                                placeholder="https://github.com/username/repository.git"
                                                value={gitUrl}
                                                onChange={(e) => setGitUrl(e.target.value)}
                                                type="url"
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Enter the clone URL for a public Git repository
                                            </p>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="git-branch" className="text-sm font-medium">
                                                Branch (optional)
                                            </Label>
                                            <Input
                                                id="git-branch"
                                                placeholder="main"
                                                value={gitBranch}
                                                onChange={(e) => setGitBranch(e.target.value)}
                                            />
                                            <p className="text-xs text-muted-foreground">
                                                Leave empty to use the default branch
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </TabsContent>
                        </Tabs>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 pt-4 border-t">
                            <Button
                                variant="outline"
                                onClick={() => setIsOpen(false)}
                                disabled={isUploading || isCreatingText || isCloning}
                            >
                                Cancel
                            </Button>
                            {activeTab === 'upload' && (
                                <Button
                                    onClick={handleFileUpload}
                                    disabled={!selectedFolder || selectedFiles.length === 0 || isUploading}
                                    className="gap-2"
                                >
                                    {isUploading ? (
                                        <>
                                            <Loader2 className="h-4 w-4 animate-spin" />
                                            Uploading...
                                        </>
                                    ) : (
                                        <>
                                            <Upload className="h-4 w-4" />
                                            Upload {selectedFiles.length} file(s)
                                        </>
                                    )}
                                </Button>
                            )}
                            {activeTab === 'text' && (
                                <Button
                                    onClick={handleTextCreate}
                                    disabled={!selectedFolder || !filename.trim() || !content.trim() || !filenameValidation.isValid || isCreatingText}
                                    className="gap-2"
                                >
                                    {isCreatingText ? (
                                        <>
                                            <Loader2 className="h-4 w-4 animate-spin" />
                                            Creating...
                                        </>
                                    ) : (
                                        <>
                                            <FileText className="h-4 w-4" />
                                            Create Entry
                                        </>
                                    )}
                                </Button>
                            )}
                            {activeTab === 'git' && (
                                <Button
                                    onClick={handleGitClone}
                                    disabled={!selectedFolder || !gitUrl.trim() || isCloning}
                                    className="gap-2"
                                >
                                    {isCloning ? (
                                        <>
                                            <Loader2 className="h-4 w-4 animate-spin" />
                                            Cloning...
                                        </>
                                    ) : (
                                        <>
                                            <GitBranch className="h-4 w-4" />
                                            Clone Repository
                                        </>
                                    )}
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}
